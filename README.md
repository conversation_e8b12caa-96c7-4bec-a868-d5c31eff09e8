
# ![RealWorld Example App](logo.png)



>  ### [Nuxt3](https://nuxt.com/) codebase containing real world examples (CRUD, auth, advanced patterns, etc) that adheres to the [RealWorld](https://github.com/gothinkster/realworld) spec and API.




### [Demo](https://nuxt-realworld.netlify.app/)&nbsp;&nbsp;&nbsp;&nbsp;[RealWorld](https://github.com/gothinkster/realworld)




This codebase was created to demonstrate a fully fledged fullstack application built with **[Nuxt3](https://nuxt.com/)** including CRUD operations, authentication, routing, pagination, and more.



We've gone to great lengths to adhere to the **[Nuxt3](https://nuxt.com/)** community styleguides & best practices.



For more information on how to this works with other frontends/backends, head over to the [RealWorld](https://github.com/gothinkster/realworld) repo.




# How it works
Look at the [Nuxt 3 documentation](https://nuxt.com/docs/getting-started/introduction) to learn more.



# Getting started

### 1. Prerequisites
-  📦 [Node.js >=16](https://nodejs.org/)
-  📦 [Yarn >=1](https://yarnpkg.com/)

<br />

### 2. Setup
First, `clone` this project and make sure to install the dependencies:

```bash
# yarn
yarn  install

# npm
npm  install

# pnpm
pnpm  install
```

<br />

### 3. Development Server

Start the development server on `http://localhost:3000`


```bash
npm  run  dev
```

<br />

### 4. Production

Build the application for production:

 ```bash
npm  run  build
```

Locally preview production build:

```bash
npm  run  preview
```


Check out the [deployment documentation](https://nuxt.com/docs/getting-started/deployment) for more information.

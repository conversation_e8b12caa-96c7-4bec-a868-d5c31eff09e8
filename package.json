{"name": "nuxt3-realworld-example-app", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "yarn run build && nuxt preview", "postinstall": "nuxt prepare", "prettier:check": "prettier --check \"./**/*.{ts,js,vue}\"", "prettier:fix": "prettier --write \"./**/*.{ts,js,vue}\"", "eslint:check": "eslint \"./**/*.{ts,js,vue}\"", "eslint:fix": "eslint --fix \"./**/*.{ts,js,vue}\"", "spell": "cspell --no-progress \"./**/*\"", "pre-commit": "yarn run prettier:check && yarn run eslint:check && yarn run spell", "lint": "yarn run pre-commit", "prepare": "husky install"}, "devDependencies": {"@nuxt/devtools": "^0.6.1", "@nuxtjs/eslint-config-typescript": "^12.0.0", "@nuxtjs/eslint-module": "^4.1.0", "@types/marked": "^5.0.0", "@types/node": "^18", "@types/sanitize-html": "^2.9.0", "@typescript-eslint/eslint-plugin": "^5.59.8", "@typescript-eslint/parser": "^5.59.8", "@vue/eslint-config-typescript": "^11.0.3", "cspell": "^6.31.1", "eslint": "^8.41.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.14.1", "husky": "^8.0.3", "nuxt": "^3.5.2", "nuxt-quasar-ui": "^1.7.4", "prettier": "^2.8.8", "sass": "^1.62.1", "typescript": "^5.0.4"}, "dependencies": {"@quasar/extras": "^1.16.4", "defu": "^6.1.2", "marked": "^5.1.0", "quasar": "^2.12.0", "sanitize-html": "^2.11.0", "vue-router": "^4.5.1"}, "engines": {"node": ">=16", "npm": ">=8", "yarn": ">=1"}}